# ORC MongoDB服务 - 统一配置文件
# 版本: 3.0.0 (重构版本)
# 简化配置结构，集成所有子服务配置

# ==================== 全局配置 ====================
project:
  name: "User-DF"
  version: "3.0.0"
  environment: "production"

# ==================== 环境配置 ====================
environment:
  project_root: "/workdir/RenL/User-DF/"

# ==================== 日志配置 ====================
logging:
  level: INFO
  format: "%(asctime)s [%(levelname)s] %(name)s: %(message)s"
  file_enabled: true
  file_path: "logs/orc_mongodb_service/orc_mongodb_service.log"
  file_max_size: "100MB"
  file_backup_count: 10
  console_enabled: true
  console_colored: true
  structured: false

# ==================== ORC处理配置 ====================
orc_processor:
  # 日期和省份配置
  start_date: "20250717"
  end_date: "20250717"
  province_ids: []  # 空数组表示处理所有省份
  
  # ORC文件配置
  orc_base_path: "/workdir/hive_data/tw_user_pic_daily_aggregation"
  orc_file_pattern: "*"
  
  # 列名映射
  column_mapping:
    uid_columns: ["id", "uid", "user_id", "userid", "UID", "USER_ID"]
    pid_columns: ["pic_id_list", "pid_list", "pid", "product_id", "item_id", "PID", "PRODUCT_ID"]
  
  # 数据处理配置
  max_pids_per_user: 200
  enable_pid_deduplication: true
  sort_pids_by_timestamp: true
  group_pids_by_timestamp: true
  
  # 分批处理配置
  batch_processing:
    batch_size: 1000
    pid_query_batch_size: 30000
    enable_batch_optimization: true
  
  # 性能优化配置
  performance:
    memory:
      gc_threshold: 0.8
      cleanup_interval: 300
    io:
      read_buffer_size: 16384
      write_buffer_size: 16384
      use_async_io: true
    concurrency:
      max_concurrent_files: 10
      max_concurrent_batches: 20

# ==================== MongoDB写入程序配置 ====================
mongodb_writer_service:
  # 批处理配置
  batch_processing:
    batch_size: 10

  # 重试配置
  retry:
    max_retries: 3
    delay: 5

  # 清理配置
  cleanup:
    delay: 300

# ==================== 监控服务配置 ====================
monitoring_service:
  # 监控配置
  monitoring:
    check_interval: 5

# ==================== Milvus配置 ====================
milvus:
  connection:
    uri: "http://10.246.85.14:19530"
    token: "nrdc_ilm:Nr@#dc12Ilm"
    database: "nrdc_db"
    
    pool:
      max_connections: 100
      min_connections: 10
      timeout: 30
      max_retries: 5
      retry_delay: 2.0
  
  collections:
    content_collection: "content_tower_collection_20250616"
    user_collection: "user_tower_collection"
  
  vector_dimensions:
    content_vector_dim: 512
    user_vector_dim: 256
  
  batch_processing:
    timeout: 600
    concurrent_batches: 10

# 是否启用Milvus PID过滤
enable_milvus_filtering: true

# ==================== Redis配置 ====================
redis:
  host: "localhost"
  port: 11005
  db: 0
  password: ""

  # 队列配置（保留兼容性）
  queue_name: "mongodb_write_queue"
  max_queue_size: 200

  # Redis Queue配置
  queue:
    # Queue名称
    queue_name: "mongodb_write_queue"
    # Queue最大长度（消息数量）
    max_length: 10000
    # 消息TTL（秒）
    message_ttl: 3600

    # 消费配置
    block_time: 1  # 阻塞等待时间（秒）

    # 重试配置
    max_retries: 3  # 最大重试次数
    retry_delay: 1.0  # 重试延迟（秒）

  # 队列长度控制配置 - 核心功能（适配Queue）
  queue_control:
    # 队列长度检查间隔（秒）
    check_interval: 10
    # 暂停阈值：Queue长度超过此值时暂停ORC处理
    pause_threshold: 150
    # 恢复阈值：Queue长度低于此值时恢复ORC处理
    resume_threshold: 20

  # 连接池配置
  connection_pool:
    max_connections: 50
    retry_on_timeout: true

# ==================== MongoDB配置 ====================
mongodb:
  # 连接配置
  connection:
    host: "localhost"
    port: 27017
    database: "nrdc"
    username: ""
    password: ""
    
    # 连接池配置
    pool:
      max_pool_size: 100
      min_pool_size: 10
      max_idle_time_ms: 30000
      connect_timeout_ms: 10000
      server_selection_timeout_ms: 30000
  
  # 集合配置
  collection: "user_pid_records_optimized"

  # 缓存配置
  cache:
    size_mb: 1024
    
  # 写入优化配置
  write_concern:
    w: 1
    j: false
    wtimeout: 10000
  
  # 批量操作配置
  bulk_operations:
    batch_size: 1000
    ordered: false
    bypass_document_validation: false

# ==================== 延迟控制配置 ====================
delay_control:
  # 批次间延迟（毫秒）
  batch_delay: 100
  # 文件处理完成后延迟计算：插入记录数/1000 * delay_multiplier
  file_delay:
    delay_multiplier: 0.5
