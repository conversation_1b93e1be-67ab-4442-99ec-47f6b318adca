#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ORC数据处理程序主入口

独立的ORC文件读取程序，负责：
- 读取ORC文件
- 通过Milvus进行PID筛选
- 将结果发送到Redis队列
"""

import os
import sys
import asyncio
import argparse
import signal
import time
from typing import Optional
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', '..', '..'))

from shared.core import ConfigManager, Logger
from services.orc_mongodb_service.orc_processor_service.service import ORCProcessor


class ProcessorRunner:
    """处理器运行器"""

    def __init__(self):
        self.processor: Optional[ORCProcessor] = None
        self.shutdown_requested = False
        self.logger = None

    def setup_signal_handlers(self):
        """设置信号处理器"""
        def signal_handler(signum, frame):
            self.logger.info(f"接收到信号 {signum}，准备停止处理...")
            self.shutdown_requested = True

        signal.signal(signal.SIGINT, signal_handler)
        signal.signal(signal.SIGTERM, signal_handler)

    async def run(self, args):
        """运行处理器"""
        try:
            # 设置环境变量（在初始化ConfigManager之前）
            if args.config:
                os.environ['USER_DF_CONFIG_FILE'] = args.config
            if args.log_level:
                os.environ['USER_DF_LOG_LEVEL'] = args.log_level

            # 初始化配置管理器
            config_manager = ConfigManager(config_file=args.config)

            # 设置独立的日志配置
            self._setup_independent_logging(config_manager)

            self.logger = Logger.get_logger(__name__)

            # 设置信号处理器
            self.setup_signal_handlers()

            self.logger.info("=== ORC数据处理程序启动 ===")
            self.logger.info(f"进程PID: {os.getpid()}")
            self.logger.info(f"日志文件: {self._get_log_file_path(config_manager)}")

            # 创建处理器实例
            self.processor = ORCProcessor(config_manager)
            await self.processor.initialize()

            # 开始处理
            await self.processor.process_files(
                start_date=args.start_date,
                end_date=args.end_date,
                province_ids=args.province_ids
            )

        except Exception as e:
            if self.logger:
                self.logger.error(f"处理失败: {e}")
            else:
                print(f"处理失败: {e}", file=sys.stderr)
            sys.exit(1)

        finally:
            if self.processor:
                await self.processor.shutdown()

    def _setup_independent_logging(self, config_manager):
        """设置独立的日志配置"""
        try:
            # 获取日志配置
            log_config = config_manager.get_config("logging", default={})

            # 为ORC处理服务设置独立的日志文件
            timestamp = int(time.time())
            log_file = f"logs/orc_mongodb_service/orc_processor_service/orc_processor_service_{timestamp}.log"

            # 确保日志目录存在
            log_dir = Path(log_file).parent
            log_dir.mkdir(parents=True, exist_ok=True)

            # 设置环境变量，让Logger使用独立的日志文件
            os.environ['USER_DF_LOG_FILE'] = log_file

        except Exception as e:
            print(f"设置独立日志配置失败: {e}", file=sys.stderr)

    def _get_log_file_path(self, config_manager):
        """获取日志文件路径"""
        return os.environ.get('USER_DF_LOG_FILE', 'logs/orc_processor_service.log')


def create_argument_parser():
    """创建命令行参数解析器"""
    parser = argparse.ArgumentParser(
        description="ORC数据处理程序",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
使用示例:
  # 处理指定日期范围的ORC文件
  python3 services/orc_processor_service/main.py --start-date 20250629 --end-date 20250630

  # 处理指定省份的ORC文件
  python3 services/orc_processor_service/main.py --province-ids 100 200 210

  # 指定配置文件
  python3 services/orc_processor_service/main.py --config configs/orc_mongodb_service/orc_processor_service/development.yaml
        """
    )

    # 处理参数
    parser.add_argument("--start-date", help="开始日期 (YYYYMMDD格式)")
    parser.add_argument("--end-date", help="结束日期 (YYYYMMDD格式)")
    parser.add_argument("--province-ids", type=int, nargs="+", help="省份ID列表")

    # 配置文件参数
    parser.add_argument("--config",
                       default="configs/orc_mongodb_service/orc_processor_service/development.yaml",
                       help="配置文件路径")

    # 日志参数
    parser.add_argument("--log-level", choices=["DEBUG", "INFO", "WARNING", "ERROR"],
                       help="日志级别")

    return parser


def validate_arguments(args):
    """验证命令行参数"""
    from datetime import datetime

    # 验证日期格式
    if args.start_date:
        try:
            datetime.strptime(args.start_date, "%Y%m%d")
        except ValueError:
            raise ValueError("start_date格式错误，应为YYYYMMDD")

    if args.end_date:
        try:
            datetime.strptime(args.end_date, "%Y%m%d")
        except ValueError:
            raise ValueError("end_date格式错误，应为YYYYMMDD")

    # 验证省份ID
    if args.province_ids:
        if not all(isinstance(pid, int) and pid > 0 for pid in args.province_ids):
            raise ValueError("province_ids必须是正整数列表")


async def main():
    """主函数"""
    try:
        # 解析命令行参数
        parser = create_argument_parser()
        args = parser.parse_args()
        
        # 验证参数
        validate_arguments(args)
        
        # 环境变量已在run方法中设置
        
        # 创建并运行处理器
        runner = ProcessorRunner()
        await runner.run(args)
        
    except Exception as e:
        print(f"启动失败: {e}", file=sys.stderr)
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())
