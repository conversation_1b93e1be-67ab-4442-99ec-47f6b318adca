#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Redis Queue操作工具类

提供Redis Queue的生产者和消费者功能：
- Queue消息生产（PUSH操作）
- 消息消费和处理（POP操作）
- 错误处理和重试机制

作者: User-DF Team
版本: 3.0.0
"""

import json
import time
import asyncio
from typing import Dict, Any, Optional, List
from dataclasses import dataclass
import redis.asyncio as redis

from ...core import Logger, NetworkException, ErrorCode


@dataclass
class QueueConfig:
    """Queue配置"""
    queue_name: str
    max_length: Optional[int] = None  # Queue最大长度，None表示不限制

    # 重试配置
    max_retries: int = 3
    retry_delay: float = 1.0

    # 消息配置
    message_ttl: Optional[int] = None  # 消息TTL（秒）





class RedisQueueProducer:
    """Redis Queue生产者"""

    def __init__(self, redis_client: redis.Redis, queue_config: QueueConfig):
        """
        初始化生产者

        Args:
            redis_client: Redis客户端
            queue_config: Queue配置
        """
        self.redis_client = redis_client
        self.config = queue_config
        self.logger = Logger.get_logger(f"RedisQueueProducer.{queue_config.queue_name}")

    async def push_message(self, data: Dict[str, Any]) -> str:
        """
        推送消息到Queue

        Args:
            data: 消息数据

        Returns:
            消息ID（时间戳）
        """
        try:
            # 生成消息ID
            message_id = str(int(time.time() * 1000000))  # 微秒级时间戳

            # 准备消息数据
            message = {
                "id": message_id,
                "data": data,
                "timestamp": int(time.time() * 1000),
                "service": data.get("service", "unknown")
            }

            # 推送消息到Queue右端（RPUSH）
            await self.redis_client.rpush(
                self.config.queue_name,
                json.dumps(message, ensure_ascii=False)
            )

            # 如果设置了最大长度，裁剪Queue
            if self.config.max_length:
                await self.redis_client.ltrim(
                    self.config.queue_name,
                    -self.config.max_length,
                    -1
                )

            self.logger.debug(f"消息已推送到Queue: {self.config.queue_name}, ID: {message_id}")
            return message_id

        except Exception as e:
            self.logger.error(f"推送消息到Queue失败: {e}")
            raise NetworkException(
                f"推送消息到Queue失败: {e}",
                ErrorCode.NETWORK_CONNECTION_ERROR
            )
    
    async def push_batch_messages(self, messages: List[Dict[str, Any]]) -> List[str]:
        """
        批量推送消息

        Args:
            messages: 消息列表

        Returns:
            消息ID列表
        """
        message_ids = []

        try:
            # 使用pipeline批量推送
            pipe = self.redis_client.pipeline()

            for message in messages:
                message_id = str(int(time.time() * 1000000))  # 微秒级时间戳
                message_ids.append(message_id)

                message_data = {
                    "id": message_id,
                    "data": message,
                    "timestamp": int(time.time() * 1000),
                    "service": message.get("service", "unknown")
                }

                pipe.rpush(
                    self.config.queue_name,
                    json.dumps(message_data, ensure_ascii=False)
                )

            # 执行批量操作
            await pipe.execute()

            # 如果设置了最大长度，裁剪Queue
            if self.config.max_length:
                await self.redis_client.ltrim(
                    self.config.queue_name,
                    -self.config.max_length,
                    -1
                )

            self.logger.debug(f"批量推送 {len(messages)} 条消息到Queue: {self.config.queue_name}")
            return message_ids

        except Exception as e:
            self.logger.error(f"批量推送消息到Queue失败: {e}")
            raise NetworkException(
                f"批量推送消息到Queue失败: {e}",
                ErrorCode.NETWORK_CONNECTION_ERROR
            )
    
    async def get_queue_info(self) -> Dict[str, Any]:
        """获取Queue信息"""
        try:
            length = await self.redis_client.llen(self.config.queue_name)
            return {
                "length": length,
                "queue_name": self.config.queue_name
            }
        except Exception as e:
            self.logger.warning(f"获取Queue信息失败: {e}")
            return {"length": 0}


class RedisQueueConsumer:
    """Redis Queue消费者"""

    def __init__(self, redis_client: redis.Redis, queue_config: QueueConfig, block_time: int = 1):
        """
        初始化消费者

        Args:
            redis_client: Redis客户端
            queue_config: Queue配置
            block_time: 阻塞等待时间（秒）
        """
        self.redis_client = redis_client
        self.queue_config = queue_config
        self.block_time = block_time
        self.logger = Logger.get_logger(f"RedisQueueConsumer.{queue_config.queue_name}")

        self._is_running = False

    async def initialize(self):
        """初始化消费者"""
        try:
            # Queue不需要特殊初始化，只需要确保Redis连接正常
            await self.redis_client.ping()
            self.logger.info(f"Queue消费者初始化完成")

        except Exception as e:
            self.logger.error(f"初始化Queue消费者失败: {e}")
            raise NetworkException(
                f"初始化Queue消费者失败: {e}",
                ErrorCode.NETWORK_CONNECTION_ERROR
            )
    
    async def pop_messages(self, callback) -> None:
        """
        弹出消息（消费消息）

        Args:
            callback: 消息处理回调函数
        """
        self._is_running = True
        self.logger.info(f"开始从Queue弹出消息: {self.queue_config.queue_name}")

        loop_count = 0
        while self._is_running:
            try:
                loop_count += 1
                self.logger.debug(f"消费循环第 {loop_count} 次迭代")

                # 从Queue左端阻塞弹出消息（BLPOP）
                self.logger.debug(f"开始弹出新消息")
                result = await self.redis_client.blpop(
                    self.queue_config.queue_name,
                    timeout=self.block_time
                )

                if result:
                    _, message_data = result
                    self.logger.debug(f"弹出1条消息")

                    try:
                        # 解析消息
                        message = json.loads(message_data)
                        message_id = message.get("id", str(int(time.time() * 1000000)))
                        data = message.get("data", {})

                        # 处理消息
                        await callback(data, message_id, message)
                        self.logger.debug("消息处理完成")

                    except json.JSONDecodeError as e:
                        self.logger.error(f"消息数据解析失败: {e}")
                    except Exception as e:
                        self.logger.error(f"处理消息失败: {e}")
                        # Queue模式下消息已经被pop，无法重试

                else:
                    # 超时没有消息
                    self.logger.debug("没有新消息，继续等待")

            except asyncio.CancelledError:
                self.logger.info("消费任务被取消")
                break
            except redis.ConnectionError as e:
                self.logger.error(f"Redis连接错误: {e}")
                await asyncio.sleep(5)  # 连接错误时等待更长时间
            except Exception as e:
                self.logger.error(f"消费消息时发生错误: {e}")
                import traceback
                self.logger.error(f"错误堆栈: {traceback.format_exc()}")
                await asyncio.sleep(1)  # 错误时短暂休眠

        self.logger.info("消费循环已结束")

    async def get_queue_length(self) -> int:
        """获取Queue长度"""
        try:
            return await self.redis_client.llen(self.queue_config.queue_name)
        except Exception as e:
            self.logger.warning(f"获取Queue长度失败: {e}")
            return 0

    async def stop(self):
        """停止消费"""
        self._is_running = False
        self.logger.info("消费者已停止")

    async def get_consumer_info(self) -> Dict[str, Any]:
        """获取消费者信息"""
        try:
            length = await self.redis_client.llen(self.queue_config.queue_name)
            return {
                "queue_name": self.queue_config.queue_name,
                "length": length
            }
        except Exception as e:
            self.logger.warning(f"获取消费者信息失败: {e}")
            return {}


# 完全使用Queue操作，不再提供List兼容性
